# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules

# next.js
/.next/
/out/

# production
/build

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# --- General ---
# Operating System files
.DS_Store
Thumbs.db
*.swp
*~

# Log files
*.log
logs
*.log.*

# Environment variables - NEVER commit sensitive keys!

# IDE/Editor directories/files
.vscode/
.idea/
*.sublime-project
*.sublime-workspace
nbproject/

# --- Node.js / npm / yarn (Frontend & Node Backend) ---
# Dependency directories
node_modules/
jspm_packages/

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Lock files (optional, but often committed to ensure consistent installs)
# package-lock.json (If you commit this, comment out the line below)
# yarn.lock (If you commit this, comment out the line below)
# pnpm-lock.yaml (If you commit this, comment out the line below)

# Build output / Distribution folders
dist/
build/
out/
coverage/

# --- Next.js Frontend Specific (in root or ./app) ---
.next/
next-env.d.ts

# --- Node.js Backend Specific (in ./node_backend) ---
# Add any specific build output or temporary files if created by your Node backend

# --- Python Backend Specific (in ./python_backend) ---
# Virtual environment folders (Common names)
venv/
env/
.venv/

ENV/
*/venv/
*/env/
*/.venv/


# Python cache and compiled files
__pycache__/
*.py[cod]
*$py.class

# Distribution / packaging
dist/
*.egg-info/
.eggs/
*.egg
*.whl
*.manifest
*.spec

# Testing / Coverage
.pytest_cache/
.coverage
.coverage.*
coverage.xml
htmlcov/
.tox/
nosetests.xml

# Temporary Downloaded Media (from your script)
python_backend/tweet_media/
# Or if the path is relative to the script location:
# tweet_media/ 

# Jupyter Notebook checkpoints
.ipynb_checkpoints

# pyenv
.python-version

# mypy cache
.mypy_cache/

# Type checker cache
.dmypy.json
dmypy.json

# --- Docker Specific ---
# Optionally ignore Docker build artifacts if stored locally near source
# (Often handled by .dockerignore, but can be added here too for local git)
# docker-compose.override.yml # Often contains local overrides not meant for repo

# --- Misc ---
# Files related to specific libraries or tools you might use
# Example: Database files (if local sqlite)
# *.sqlite
# *.db