/* Main container */
.main-container {
  min-height: 100vh;
  background: var(--background-gradient);
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 32px 16px;
}

/* Header styles */
.header {
  text-align: center;
  margin-bottom: 40px;
  animation: fadeIn 0.8s ease-out;
}

.title {
  font-size: clamp(1.8rem, 5vw, 2.5rem);
  font-weight: 700;
  color: var(--primary-dark);
  margin-bottom: 8px;
  background: linear-gradient(45deg, var(--primary-dark), var(--primary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.subtitle {
  color: var(--text-light);
  font-size: clamp(0.875rem, 2vw, 1rem);
  letter-spacing: 0.5px;
}

/* Card styles */
.card {
  max-width: 1000px;
  margin: 0 auto;
  background: var(--glass-background);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: 24px;
  border: var(--glass-border);
  animation: slideUp 0.6s ease-out;
  overflow: hidden;
  position: relative;
}

.card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  position: sticky;
  top: 0;
  background: var(--glass-background);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  z-index: 10;
  padding: 16px 0;
  margin: -16px 0 24px 0;
}

.card-title {
  font-size: clamp(1.2rem, 3vw, 1.5rem);
  font-weight: 600;
  color: var(--text-color);
}

.button-group {
  display: flex;
  gap: 12px;
}

/* Button styles */
.add-button,
.clear-button {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  font-weight: 500;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: var(--border-radius-sm);
  transition: var(--transition);
}

.add-button {
  color: var(--primary-color);
}

.add-button:hover {
  background-color: var(--primary-lightest);
  color: var(--primary-dark);
  transform: translateY(-2px);
}

.clear-button {
  color: var(--text-light);
}

.clear-button:hover {
  background-color: var(--muted-color);
  color: var(--text-color);
  transform: translateY(-2px);
}

.plus-icon,
.clear-icon {
  margin-right: 8px;
  height: 20px;
  width: 20px;
}

.run-button {
  background: linear-gradient(45deg, var(--primary-color), #ff0080);
  color: white;
  border: none;
  padding: 10px 18px;
  border-radius: var(--border-radius-sm);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  min-width: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 6px rgba(219, 39, 119, 0.2);
}

.run-button:hover:not(:disabled) {
  background: linear-gradient(45deg, #ff0080, var(--primary-color));
  transform: translateY(-2px);
  box-shadow: 0 6px 10px rgba(219, 39, 119, 0.3);
}

.run-button:active:not(:disabled) {
  animation: pulse 0.3s ease;
}

.run-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.delete-button {
  background: none;
  border: none;
  color: var(--text-light);
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
}

.delete-button:hover {
  background-color: var(--danger-light);
  color: var(--danger-color);
  transform: rotate(5deg);
}

.copy-button {
  display: flex;
  align-items: center;
  gap: 6px;
  background-color: var(--muted-color);
  border: none;
  color: var(--text-light);
  padding: 8px 14px;
  border-radius: var(--border-radius-sm);
  font-size: 0.875rem;
  cursor: pointer;
  transition: var(--transition);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.copy-button:hover {
  background-color: var(--muted-hover);
  color: var(--text-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.copy-button.copied {
  background-color: var(--success-light);
  color: var(--success-color);
}

.copy-icon,
.check-icon {
  width: 16px;
  height: 16px;
}

/* Fields container */
.fields-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
}

.field-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  border: 1px solid transparent;
  border-radius: var(--border-radius);
  padding: 20px;
  background: rgba(253, 242, 248, 0.7);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.05), 0 4px 6px -2px rgba(0, 0, 0, 0.025);
  transition: var(--transition);
  animation: slideIn 0.4s ease-out;
  position: relative;
  overflow: hidden;
}

.field-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
}

.field-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.field-row {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* Input styles */
.input-container {
  flex-grow: 1;
}

.text-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--primary-lighter);
  border-radius: var(--border-radius-sm);
  font-size: 1rem;
  transition: var(--transition);
  color: var(--text-color);
  background-color: rgba(255, 255, 255, 0.8);
  box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.02), -5px -5px 10px rgba(255, 255, 255, 0.5);
}

.text-input::placeholder {
  color: var(--text-lighter);
}

.text-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(219, 39, 119, 0.2);
  transform: translateY(-2px);
}

/* Response styles */
.response-container {
  background: var(--glass-background);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border-radius: var(--border-radius-sm);
  padding: 20px;
  box-shadow: var(--box-shadow-sm);
  border: var(--glass-border);
  animation: fadeIn 0.5s ease-out;
}

.response-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  border-bottom: 1px solid var(--primary-light);
  padding-bottom: 12px;
}

.response-tabs {
  display: flex;
  gap: 4px;
}

.tab-button {
  padding: 8px 16px;
  background: none;
  border: none;
  border-bottom: 3px solid transparent;
  font-weight: 500;
  color: var(--text-light);
  cursor: pointer;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.tab-button::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
  transform: scaleX(0);
  transform-origin: bottom right;
  transition: transform 0.3s ease;
}

.tab-button:hover {
  color: var(--primary-color);
}

.tab-button:hover::after {
  transform: scaleX(1);
  transform-origin: bottom left;
}

.tab-button.active {
  color: var(--primary-color);
}

.tab-button.active::after {
  transform: scaleX(1);
}

.response-content {
  font-size: 1.1rem;
  line-height: 1.6;
  color: var(--text-color);
  white-space: pre-line;
}

.response-content p {
  margin-bottom: 12px;
  animation: fadeIn 0.5s ease-out;
  animation-fill-mode: both;
}

.response-content p:nth-child(1) {
  animation-delay: 0.1s;
}
.response-content p:nth-child(2) {
  animation-delay: 0.2s;
}
.response-content p:nth-child(3) {
  animation-delay: 0.3s;
}
.response-content p:nth-child(4) {
  animation-delay: 0.4s;
}
.response-content p:nth-child(5) {
  animation-delay: 0.5s;
}

.response-content p:last-child {
  margin-bottom: 0;
}

/* Analysis cards */
.analysis-card {
  border-radius: var(--border-radius-sm);
  padding: 16px;
  margin-bottom: 16px;
  background: white;
  box-shadow: var(--box-shadow-sm);
  display: flex;
  gap: 12px;
  transition: var(--transition);
}

.analysis-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

.analysis-icon {
  width: 40px;
  height: 40px;
  background: var(--primary-lightest);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
}

.analysis-content {
  flex: 1;
}

/* Engagement bars */
.engagement-bar {
  height: 8px;
  background: var(--muted-color);
  border-radius: 4px;
  margin: 8px 0;
  overflow: hidden;
}

.engagement-value {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
  width: var(--percentage, 50%);
  transition: width 1s ease;
}

/* Loading animation */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loader {
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top: 2px solid white;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  animation: spin 1s linear infinite;
}

/* Floating action button */
.floating-add-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(45deg, var(--primary-color), #ff0080);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(219, 39, 119, 0.3);
  z-index: 100;
  cursor: pointer;
  transition: var(--transition);
  color: white;
  border: none;
}

.floating-add-button:hover {
  transform: translateY(-5px) rotate(90deg);
  box-shadow: 0 6px 16px rgba(219, 39, 119, 0.4);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Browser compatibility */
button,
input,
select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

/* Responsive styles */
@media (max-width: 768px) {
  .fields-container {
    grid-template-columns: 1fr;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .field-row {
    flex-direction: column;
    align-items: stretch;
  }

  .response-content {
    font-size: 1rem;
  }

  .response-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .response-tabs {
    overflow-x: auto;
    width: 100%;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Firefox */
  }

  .response-tabs::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Edge */
  }

  .tab-button {
    white-space: nowrap;
  }

  .run-button,
  .delete-button,
  .copy-button {
    min-height: 48px;
    min-width: 48px;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 2rem;
  }

  .card {
    padding: 16px;
  }

  .field-container {
    padding: 16px;
  }

  .button-group {
    width: 100%;
    justify-content: space-between;
  }

  .floating-add-button {
    bottom: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .field-container {
    background: rgba(18, 18, 18, 0.7);
  }

  .text-input {
    background-color: rgba(30, 30, 30, 0.8);
    box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.2), -5px -5px 10px rgba(255, 255, 255, 0.02);
  }

  .analysis-card {
    background: var(--muted-color);
  }
}

/* Print styles */
@media print {
  .main-container {
    background: white;
  }

  .card {
    box-shadow: none;
    border: 1px solid #ddd;
  }

  .add-button,
  .run-button,
  .delete-button,
  .clear-button,
  .copy-button,
  .floating-add-button {
    display: none;
  }
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

