import "./globals.css"
import "./styles.css"
import type { ReactNode } from "react"

export const metadata = {
  title: "NYX Professional Makeup",
  description: "",
    generator: 'v0.dev'
}

interface RootLayoutProps {
  children: ReactNode
}

export default function RootLayout({ children }: RootLayoutProps) {
  return (
    <html lang="en">
      <body>{children}</body>
    </html>
  )
}



import './globals.css'