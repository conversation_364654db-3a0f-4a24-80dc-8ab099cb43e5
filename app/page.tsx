"use client"

import { stringify } from "querystring"
import type React from "react"

import { useState, useEffect } from "react"
import type { JSX } from "react/jsx-runtime"

// Define TypeScript interfaces for our data structures
interface TwitterField {
  id: string
  value: string
  isLoading: boolean
  responses: string[] | null
  activeResponse?: number
}

interface CopyStatusState {
  [key: string]: boolean
}

export default function Home() {
  const [twitterFields, setTwitterFields] = useState<TwitterField[]>([
    {
      id: "1",
      value: "",
      isLoading: false,
      responses: null,
    },
  ])

  const [copyStatus, setCopyStatus] = useState<CopyStatusState>({})
  const [darkMode, setDarkMode] = useState<boolean>(false)

  const [tiktokFields, setTiktokFields] = useState<TwitterField[]>([
    {
      id: Date.now().toString(),
      value: "",
      isLoading: false,
      responses: null,
    },
  ])
const [instagram<PERSON>ields, setInstagram<PERSON>ields] = useState<TwitterField[]>([
  {
    id: Date.now().toString(),
    value: "",
    isLoading: false,
    responses: null,
  },
])

  // Check system preference for dark mode
  useEffect(() => {
    const darkModeMediaQuery = window.matchMedia("(prefers-color-scheme: dark)")
    setDarkMode(darkModeMediaQuery.matches)

    const handler = (e: MediaQueryListEvent) => setDarkMode(e.matches)
    darkModeMediaQuery.addEventListener("change", handler)

    return () => darkModeMediaQuery.removeEventListener("change", handler)
  }, [])

  // Animate response containers when they appear
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("visible")
          }
        })
      },
      { threshold: 0.1 },
    )

    document.querySelectorAll(".response-container").forEach((el) => {
      observer.observe(el)
    })

    return () => {
      document.querySelectorAll(".response-container").forEach((el) => {
        observer.unobserve(el)
      })
    }
  }, [twitterFields])

  const addField = (): void => {
    setTwitterFields([
      ...twitterFields,
      {
        id: Date.now().toString(),
        value: "",
        isLoading: false,
        responses: null,
      },
    ])
  }

  const updateFieldValue = (id: string, value: string): void => {
    setTwitterFields(twitterFields.map((field) => (field.id === id ? { ...field, value } : field)))
  }

  const deleteField = (id: string): void => {
    setTwitterFields(twitterFields.filter((field) => field.id !== id))
  }

  const clearAllFields = (): void => {
    setTwitterFields([
      {
        id: Date.now().toString(),
        value: "",
        isLoading: false,
        responses: null,
      },
    ])
  }

  // --- NEW: Function to clear responses for a single field ---
  const clearResponses = (id: string): void => {
    setTwitterFields(
      twitterFields.map((field) =>
        field.id === id
          ? { ...field, responses: null, isLoading: false, activeResponse: undefined } // Reset responses and loading state
          : field,
      ),
    );
  };
  // --- End NEW ---

  const runAnalysis = async (id: string): Promise<void> => {
    const fieldToUpdate = twitterFields.find((field) => field.id === id);
    if (!fieldToUpdate) return; // Should not happen, but good practice

    // Set loading state
    setTwitterFields(twitterFields.map((field) => (field.id === id ? { ...field, isLoading: true, responses: null } : field))); // Reset responses

    try {
      // --- EDIT: Call the Node.js backend API ---
      const backendUrl = "http://localhost:3001/api/process-tweet"; // Assuming Node.js runs on the same host/port or proxied
      const response = await fetch(backendUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        // Send the Twitter ID (which is the field value) as tweetId
        body: JSON.stringify({ tweetId: fieldToUpdate.value }), 
      });

      if (!response.ok) {
        // Handle API errors (e.g., tweet not found, backend issues)
        const errorData = await response.json();
        console.error("API Error:", errorData);
        // Display error to the user (optional)
         setTwitterFields(
            twitterFields.map((field) =>
                field.id === id
                ? {
                    ...field,
                    isLoading: false,
                    // You could add an error message to the field state here
                    responses: [`Error: ${errorData.message || 'Failed to fetch analysis'}`], 
                 }
                : field
            )
         );
        return; 
      }

      const data = await response.json();

      const analysisResponses: string[] = data.response || []; 

      setTwitterFields(

        twitterFields.map((field) =>
          field.id === id
            ? {
                ...field,
                isLoading: false,
                responses: analysisResponses.length > 0 ? analysisResponses : ['No analysis generated.'], // Use actual responses
                activeResponse: 0, 
              }
            : field,
        ),
      );
    } catch (error) {
      // Handle network errors or other exceptions
      console.error("Failed to run analysis:", error);
       setTwitterFields(
         twitterFields.map((field) =>
           field.id === id
             ? {
                 ...field,
                 isLoading: false,
                  // Display generic error
                 responses: [`Error: Network error or backend unavailable.`],
               }
             : field,
         ),
       );
    }
  };

  const switchResponse = (fieldId: string, responseIndex: number): void => {
    setTwitterFields(
      twitterFields.map((field) => (field.id === fieldId ? { ...field, activeResponse: responseIndex } : field)),
    )
  }

  const copyToClipboard = async (fieldId: string, responseIndex: number): Promise<void> => {
    const field = twitterFields.find((f) => f.id === fieldId)
    if (field && field.responses) {
      try {
        await navigator.clipboard.writeText(field.responses[responseIndex])

        // Set copy status for this specific field/response
        setCopyStatus({
          ...copyStatus,
          [`${fieldId}-${responseIndex}`]: true,
        })

        // Reset copy status after 2 seconds
        setTimeout(() => {
          setCopyStatus({
            ...copyStatus,
            [`${fieldId}-${responseIndex}`]: false,
          })
        }, 2000)
      } catch (err) {
        console.error("Failed to copy text: ", err)
      }
    }
  }

  // ADD
  const addTikTokField = (): void => {
    setTiktokFields([
      ...tiktokFields,
      {
        id: Date.now().toString(),
        value: "",
        isLoading: false,
        responses: null,
      },
    ])
  }

  // UPDATE
  const updateTikTokFieldValue = (id: string, value: string): void => {
    setTiktokFields(
      tiktokFields.map((f) => (f.id === id ? { ...f, value } : f))
    )
  }

  // DELETE
  const deleteTikTokField = (id: string): void => {
    setTiktokFields(tiktokFields.filter((f) => f.id !== id))
  }

  // CLEAR ALL
  const clearAllTikTokFields = (): void => {
    setTiktokFields([
      {
        id: Date.now().toString(),
        value: "",
        isLoading: false,
        responses: null,
      },
    ])
  }

  // CLEAR RESPONSES
  const clearTikTokResponses = (id: string): void => {
    setTiktokFields(
      tiktokFields.map((f) =>
        f.id === id
          ? { ...f, responses: null, isLoading: false, activeResponse: undefined }
          : f
      )
    )
  }

  // RUN ANALYSIS
  const runTikTokAnalysis = async (id: string): Promise<void> => {
    const field = tiktokFields.find((f) => f.id === id)
    if (!field) return

    setTiktokFields(
      tiktokFields.map((f) =>
        f.id === id ? { ...f, isLoading: true, responses: null } : f
      )
    )

    try {
      const res = await fetch("http://localhost:8000/generate_tiktok_response", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ video_url: field.value }),
      })

      if (!res.ok) {
        const errorData = await res.json()
        setTiktokFields(
          tiktokFields.map((f) =>
            f.id === id
              ? {
                  ...f,
                  isLoading: false,
                  responses: [`Error: ${errorData.message || "Failed"}`],
                }
              : f
          )
        )
        return
      }

      const data = await res.json()
      const resp = data.response || []
      setTiktokFields(
        tiktokFields.map((f) =>
          f.id === id
            ? {
                ...f,
                isLoading: false,
                responses: resp.length ? resp : ["No analysis generated."],
                activeResponse: 0,
              }
            : f
        )
      )
    } catch (err) {
      console.error(err)
      setTiktokFields(
        tiktokFields.map((f) =>
          f.id === id
            ? {
                ...f,
                isLoading: false,
                responses: ["Error: Network or backend unavailable."],
              }
            : f
        )
      )
    }
  }

  // SWITCH RESPONSE TAB
  const switchTikTokResponse = (fieldId: string, idx: number): void => {
    setTiktokFields(
      tiktokFields.map((f) =>
        f.id === fieldId ? { ...f, activeResponse: idx } : f
      )
    )
  }

  // COPY TO CLIPBOARD
  const copyTikTokToClipboard = async (
    fieldId: string,
    responseIndex: number
  ): Promise<void> => {
    const field = tiktokFields.find((f) => f.id === fieldId)
    if (!field?.responses) return

    try {
      await navigator.clipboard.writeText(field.responses[responseIndex])
      setCopyStatus({
        ...copyStatus,
        [`tiktok-${fieldId}-${responseIndex}`]: true,
      })
      setTimeout(() => {
        setCopyStatus({
          ...copyStatus,
          [`tiktok-${fieldId}-${responseIndex}`]: false,
        })
      }, 2000)
    } catch (e) {
      console.error("Copy failed", e)
    }
  }

// ADD
const addInstagramField = (): void => {
  setInstagramFields([
    ...instagramFields,
    {
      id: Date.now().toString(),
      value: "",
      isLoading: false,
      responses: null,
    },
  ])
}

// UPDATE
const updateInstagramFieldValue = (id: string, value: string): void => {
  setInstagramFields(
    instagramFields.map((f) => (f.id === id ? { ...f, value } : f))
  )
}

// DELETE
const deleteInstagramField = (id: string): void => {
  setInstagramFields(instagramFields.filter((f) => f.id !== id))
}

// CLEAR ALL
const clearAllInstagramFields = (): void => {
  setInstagramFields([
    {
      id: Date.now().toString(),
      value: "",
      isLoading: false,
      responses: null,
    },
  ])
}

// CLEAR RESPONSES
const clearInstagramResponses = (id: string): void => {
  setInstagramFields(
    instagramFields.map((f) =>
      f.id === id
        ? { ...f, responses: null, isLoading: false, activeResponse: undefined }
        : f
    )
  )
}

// RUN ANALYSIS
const runInstagramAnalysis = async (id: string): Promise<void> => {
  const field = instagramFields.find((f) => f.id === id)
  if (!field) return

  setInstagramFields(
    instagramFields.map((f) =>
      f.id === id ? { ...f, isLoading: true, responses: null } : f
    )
  )

  try {
    const url = field.value?.trim()
    const match = url?.match(/\/(?:reel|p)\/([^/?#]+)/)
    const shortcode = match?.[1]

    if (!shortcode) {
      setInstagramFields(
        instagramFields.map((f) =>
          f.id === id
            ? {
                ...f,
                isLoading: false,
                responses: ["Error: Invalid Instagram post or reel URL."],
              }
            : f
        )
      )
      return
    }

    const res = await fetch(`http://localhost:3010/api/instagram/p/${shortcode}`)
    const data = await res.json()

    if (!res.ok) {
      setInstagramFields(
        instagramFields.map((f) =>
          f.id === id
            ? {
                ...f,
                isLoading: false,
                responses: [`Error: ${data.message || "Failed"}`],
              }
            : f
        )
      )
      return
    }

    if (data.isPhoto) {
        console.log("x")
        const send_photo_data = await fetch("http://localhost:8000/generate_instagram_response_photo", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ photo_data: data }),
      })  

    if (!send_photo_data.ok) {
        const errorData = await send_photo_data.json()
        setInstagramFields(
          instagramFields.map((f) =>
            f.id === id
              ? {
                  ...f,
                  isLoading: false,
                  responses: [`Error: ${errorData.message || "Failed"}`],
                }
              : f
          )
        )
        return
      }

      const insta_data = await send_photo_data.json()
      const resp = insta_data.response || []
      setInstagramFields(
        instagramFields.map((f) =>
          f.id === id
            ? {
                ...f,
                isLoading: false,
                responses: resp.length ? resp : ["No analysis generated."],
                activeResponse: 0,
              }
            : f
        )
      )

      } else {
  
    const download_url = await fetch(`http://localhost:3010/api/download-proxy?url=${encodeURIComponent(data.data.xdt_shortcode_media.video_url)}&filename=${encodeURIComponent(`${shortcode}.mp4`)}`)
    const download_url_data = await download_url.json()
    if (!download_url.ok) {
      setInstagramFields(
        instagramFields.map((f) =>
          f.id === id
            ? {
                ...f,
                isLoading: false,
                responses: [`Error: ${download_url_data.message || "Failed to fetch download URL"}`],
              }
            : f
        )
      )
      return
    }
    const insta_endpoint = await fetch("http://localhost:8000/generate_instagram_response_video", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ path: download_url_data.path }),
      })
    if (!insta_endpoint.ok) {
        const errorData = await insta_endpoint.json()
        setInstagramFields(
          instagramFields.map((f) =>
            f.id === id
              ? {
                  ...f,
                  isLoading: false,
                  responses: [`Error: ${errorData.message || "Failed"}`],
                }
              : f
          )
        )
        return
      }

      const insta_data = await insta_endpoint.json()
      const resp = insta_data.response || []
      setInstagramFields(
        instagramFields.map((f) =>
          f.id === id
            ? {
                ...f,
                isLoading: false,
                responses: resp.length ? resp : ["No analysis generated."],
                activeResponse: 0,
              }
            : f
        )
      )
    }

  } catch (err) {
    console.error(err)
    setInstagramFields(
      instagramFields.map((f) =>
        f.id === id
          ? {
              ...f,
              isLoading: false,
              responses: ["Error: Network or backend unavailable."],
            }
          : f
      )
    )
  }
}


// SWITCH RESPONSE TAB
const switchInstagramResponse = (fieldId: string, idx: number): void => {
  setInstagramFields(
    instagramFields.map((f) =>
      f.id === fieldId ? { ...f, activeResponse: idx } : f
    )
  )
}

// COPY TO CLIPBOARD
const copyInstagramToClipboard = async (
  fieldId: string,
  responseIndex: number
): Promise<void> => {
  const field = instagramFields.find((f) => f.id === fieldId)
  if (!field?.responses) return

  try {
    await navigator.clipboard.writeText(field.responses[responseIndex])
    setCopyStatus({
      ...copyStatus,
      [`instagram-${fieldId}-${responseIndex}`]: true,
    })
    setTimeout(() => {
      setCopyStatus({
        ...copyStatus,
        [`instagram-${fieldId}-${responseIndex}`]: false,
      })
    }, 2000)
  } catch (e) {
    console.error("Copy failed", e)
  }
}

// ...existing code...

  // Function to render engagement bars

  return (
    <main className="main-container">
      <div className="container">
        <header className="header">
          <h1 className="title">NYX Professional Makeup</h1>
          <p className="subtitle"></p>
        </header>

        <div className="card">
          <div className="card-header">
            <h2 className="card-title">Twitter ID</h2>
            <div className="button-group">
              <button onClick={clearAllFields} className="clear-button">
                <svg
                  className="clear-icon"
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M3 6h18"></path>
                  <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                  <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                  <line x1="10" y1="11" x2="10" y2="17"></line>
                  <line x1="14" y1="11" x2="14" y2="17"></line>
                </svg>
                Clear All
              </button>
              <button onClick={addField} className="add-button">
                <svg
                  className="plus-icon"
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="8" x2="12" y2="16"></line>
                  <line x1="8" y1="12" x2="16" y2="12"></line>
                </svg>
                Add Field
              </button>
            </div>
          </div>

          <div className="fields-container">
            {twitterFields.map((field) => (
              <div key={field.id} className="field-container">
                <div className="field-row">
                  <div className="input-container">
                    <input
                      type="text"
                      placeholder="Enter Twitter ID"
                      value={field.value}
                      onChange={(e) => updateFieldValue(field.id, e.target.value)}
                      className="text-input"
                    />
                  </div>

                  {!field.isLoading && !field.responses && (
                    <>
                      <button onClick={() => runAnalysis(field.id)} disabled={!field.value} className="run-button">
                        Run
                      </button>
                      <button onClick={() => deleteField(field.id)} className="delete-button">
                        <svg
                          className="delete-icon"
                          xmlns="http://www.w3.org/2000/svg"
                          width="18"
                          height="18"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <path d="M3 6h18"></path>
                          <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                          <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                        </svg>
                      </button>
                    </>
                  )}

                  {field.isLoading && (
                    <button disabled className="run-button loading">
                      <span className="loader"></span>
                      Running
                    </button>
                  )}

                  {field.responses && (
                    <>
                      {/* --- NEW: Clear Responses Button --- */}
                      <button 
                        onClick={() => clearResponses(field.id)} 
                        className="clear-responses-button delete-button" 
                        title="Clear Responses"
                      >
                        <svg 
                          xmlns="http://www.w3.org/2000/svg" 
                          width="18" 
                          height="18" 
                          viewBox="0 0 24 24" 
                          fill="none" 
                          stroke="currentColor" 
                          strokeWidth="2" 
                          strokeLinecap="round" 
                          strokeLinejoin="round"
                         >
                           <polyline points="23 4 23 10 17 10"></polyline>
                           <polyline points="1 4 1 10 7 10"></polyline>
                           <path d="M3.51 9a9 9 0 0 1 14.85-3.36L20.49 9"></path>
                           <path d="M20.49 15a9 9 0 0 1-14.85 3.36L3.51 15"></path>
                         </svg>
                       </button>
                       {/* --- End NEW --- */}

                       <button onClick={() => deleteField(field.id)} className="delete-button" title="Delete Field">
                         <svg
                           className="delete-icon"
                           xmlns="http://www.w3.org/2000/svg"
                           width="18"
                           height="18"
                           viewBox="0 0 24 24"
                           fill="none"
                           stroke="currentColor"
                           strokeWidth="2"
                           strokeLinecap="round"
                           strokeLinejoin="round"
                         >
                           <path d="M3 6h18"></path>
                           <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                           <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                         </svg>
                       </button>
                    </>
                  )}
                </div>

                {field.responses && (
                  <div className="response-container">
                    <div className="response-header">
                      <div className="response-tabs">
                        <button
                          className={`tab-button ${field.activeResponse === 0 ? "active" : ""}`}
                          onClick={() => switchResponse(field.id, 0)}
                        >
                          Response
                        </button>
                        <button
                          className={`tab-button ${field.activeResponse === 1 ? "active" : ""}`}
                          onClick={() => switchResponse(field.id, 1)}
                        >
                          Response
                        </button>
                        <button
                          className={`tab-button ${field.activeResponse === 2 ? "active" : ""}`}
                          onClick={() => switchResponse(field.id, 2)}
                        >
                          Response
                        </button>
                      </div>
                      <button
                        onClick={() => copyToClipboard(field.id, field.activeResponse || 0)}
                        className={`copy-button ${copyStatus[`${field.id}-${field.activeResponse}`] ? "copied" : ""}`}
                      >
                        {copyStatus[`${field.id}-${field.activeResponse}`] ? (
                          <>
                            <svg
                              className="check-icon"
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <polyline points="20 6 9 17 4 12"></polyline>
                            </svg>
                            Copied!
                          </>
                        ) : (
                          <>
                            <svg
                              className="copy-icon"
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                              <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                            </svg>
                            Copy
                          </>
                        )}
                      </button>
                    </div>

                    {/* Enhanced response content with visual elements */}
                    <div className="response-content">
                      {field.responses[field.activeResponse || 0].split("\n").map((line, index) => (
                        <p key={index}>{line}</p>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
    <div className="card">
    <div className="card-header">
      <h2 className="card-title">TikTok Video Link</h2>
      <div className="button-group">
        <button
          onClick={clearAllTikTokFields}
          className="clear-button"
        >
                          <svg
                  className="clear-icon"
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M3 6h18"></path>
                  <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                  <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                  <line x1="10" y1="11" x2="10" y2="17"></line>
                  <line x1="14" y1="11" x2="14" y2="17"></line>
                </svg> Clear All
        </button>
        <button onClick={addTikTokField} className="add-button">
        <svg
                  className="plus-icon"
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="8" x2="12" y2="16"></line>
                  <line x1="8" y1="12" x2="16" y2="12"></line>
                </svg> Add Field
        </button>
      </div>
    </div>

    <div className="fields-container">
      {tiktokFields.map((field) => (
        <div key={field.id} className="field-container">
          <div className="field-row">
            <div className="input-container">
              <input
                type="text"
                placeholder="Enter TikTok ID"
                value={field.value}
                onChange={(e) =>
                  updateTikTokFieldValue(field.id, e.target.value)
                }
                className="text-input"
              />
            </div>

            {!field.isLoading && !field.responses && (
              <>
                <button
                  onClick={() => runTikTokAnalysis(field.id)}
                  disabled={!field.value}
                  className="run-button"
                >
                  Run
                </button>
                <button
                  onClick={() => deleteTikTokField(field.id)}
                  className="delete-button"
                >
                  <svg
                           className="delete-icon"
                           xmlns="http://www.w3.org/2000/svg"
                           width="18"
                           height="18"
                           viewBox="0 0 24 24"
                           fill="none"
                           stroke="currentColor"
                           strokeWidth="2"
                           strokeLinecap="round"
                           strokeLinejoin="round"
                         >
                           <path d="M3 6h18"></path>
                           <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                           <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                         </svg>
                </button>
              </>
            )}

            {field.isLoading && (
              <button disabled className="run-button loading">
                <span className="loader"></span> Running
              </button>
            )}

            {field.responses && (
              <>
                <button
                  onClick={() => clearTikTokResponses(field.id)}
                  className="clear-responses-button delete-button"
                  title="Clear Responses"
                >
                  <svg 
                          xmlns="http://www.w3.org/2000/svg" 
                          width="18" 
                          height="18" 
                          viewBox="0 0 24 24" 
                          fill="none" 
                          stroke="currentColor" 
                          strokeWidth="2" 
                          strokeLinecap="round" 
                          strokeLinejoin="round"
                         >
                           <polyline points="23 4 23 10 17 10"></polyline>
                           <polyline points="1 4 1 10 7 10"></polyline>
                           <path d="M3.51 9a9 9 0 0 1 14.85-3.36L20.49 9"></path>
                           <path d="M20.49 15a9 9 0 0 1-14.85 3.36L3.51 15"></path>
                         </svg>
                </button>
                <button
                  onClick={() => deleteTikTokField(field.id)}
                  className="delete-button"
                  title="Delete Field"
                >
                 <svg
                           className="delete-icon"
                           xmlns="http://www.w3.org/2000/svg"
                           width="18"
                           height="18"
                           viewBox="0 0 24 24"
                           fill="none"
                           stroke="currentColor"
                           strokeWidth="2"
                           strokeLinecap="round"
                           strokeLinejoin="round"
                         >
                           <path d="M3 6h18"></path>
                           <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                           <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                         </svg>
                </button>
              </>
            )}
          </div>

          {field.responses && (
            <div className="response-container">
              <div className="response-header">
                <div className="response-tabs">
                  {field.responses.map((_, idx) => (
                    <button
                      key={idx}
                      className={`tab-button ${
                        field.activeResponse === idx ? "active" : ""
                      }`}
                      onClick={() =>
                        switchTikTokResponse(field.id, idx)
                      }
                    >
                      Response {idx + 1}
                    </button>
                  ))}
                </div>
                <button
                  onClick={() =>
                    copyTikTokToClipboard(
                      field.id,
                      field.activeResponse || 0
                    )
                  }
                  className={`copy-button ${
                    copyStatus[
                      `tiktok-${field.id}-${
                        field.activeResponse || 0
                      }`
                    ]
                      ? "copied"
                      : ""
                  }`}
                >
                  {copyStatus[
                    `tiktok-${field.id}-${
                      field.activeResponse || 0
                    }`
                  ] ? (
                    <>
                      <svg
                              className="check-icon"
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <polyline points="20 6 9 17 4 12"></polyline>
                            </svg> Copied!
                    </>
                  ) : (
                    <>
                      <svg
                              className="copy-icon"
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                              <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                            </svg> Copy
                    </>
                  )}
                </button>
              </div>

              <div className="response-content">
                {field.responses[
                  field.activeResponse || 0
                ]
                  .split("\n")
                  .map((line, i) => (
                    <p key={i}>{line}</p>
                  ))}
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  </div>
  <div className="card">
  <div className="card-header">
    <h2 className="card-title">Instagram Post Link</h2>
    <div className="button-group">
      <button onClick={clearAllInstagramFields} className="clear-button">
        <svg
          className="clear-icon"
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M3 6h18"></path>
          <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
          <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
          <line x1="10" y1="11" x2="10" y2="17"></line>
          <line x1="14" y1="11" x2="14" y2="17"></line>
        </svg>
        Clear All
      </button>
      <button onClick={addInstagramField} className="add-button">
        <svg
          className="plus-icon"
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <circle cx="12" cy="12" r="10"></circle>
          <line x1="12" y1="8" x2="12" y2="16"></line>
          <line x1="8" y1="12" x2="16" y2="12"></line>
        </svg>
        Add Field
      </button>
    </div>
  </div>

  <div className="fields-container">
    {instagramFields.map((field) => (
      <div key={field.id} className="field-container">
        <div className="field-row">
          <div className="input-container">
            <input
              type="text"
              placeholder="Enter Instagram Post Link"
              value={field.value}
              onChange={(e) => updateInstagramFieldValue(field.id, e.target.value)}
              className="text-input"
            />
          </div>

          {!field.isLoading && !field.responses && (
            <>
              <button
                onClick={() => runInstagramAnalysis(field.id)}
                disabled={!field.value}
                className="run-button"
              >
                Run
              </button>
              <button
                onClick={() => deleteInstagramField(field.id)}
                className="delete-button"
              >
                <svg
                  className="delete-icon"
                  xmlns="http://www.w3.org/2000/svg"
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M3 6h18"></path>
                  <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                  <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                </svg>
              </button>
            </>
          )}

          {field.isLoading && (
            <button disabled className="run-button loading">
              <span className="loader"></span>
              Running
            </button>
          )}

          {field.responses && (
            <>
              <button
                onClick={() => clearInstagramResponses(field.id)}
                className="clear-responses-button delete-button"
                title="Clear Responses"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <polyline points="23 4 23 10 17 10"></polyline>
                  <polyline points="1 4 1 10 7 10"></polyline>
                  <path d="M3.51 9a9 9 0 0 1 14.85-3.36L20.49 9"></path>
                  <path d="M20.49 15a9 9 0 0 1-14.85 3.36L3.51 15"></path>
                </svg>
              </button>
              <button
                onClick={() => deleteInstagramField(field.id)}
                className="delete-button"
                title="Delete Field"
              >
                <svg
                  className="delete-icon"
                  xmlns="http://www.w3.org/2000/svg"
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M3 6h18"></path>
                  <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                  <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                </svg>
              </button>
            </>
          )}
        </div>

        {field.responses && (
          <div className="response-container">
            <div className="response-header">
              <div className="response-tabs">
                {field.responses.map((_, idx) => (
                  <button
                    key={idx}
                    className={`tab-button ${field.activeResponse === idx ? "active" : ""}`}
                    onClick={() => switchInstagramResponse(field.id, idx)}
                  >
                    Response {idx + 1}
                  </button>
                ))}
              </div>
              <button
                onClick={() =>
                  copyInstagramToClipboard(field.id, field.activeResponse || 0)
                }
                className={`copy-button ${
                  copyStatus[
                    `instagram-${field.id}-${field.activeResponse || 0}`
                  ]
                    ? "copied"
                    : ""
                }`}
              >
                {copyStatus[
                  `instagram-${field.id}-${field.activeResponse || 0}`
                ] ? (
                  <>
                    <svg
                      className="check-icon"
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <polyline points="20 6 9 17 4 12"></polyline>
                    </svg>
                    Copied!
                  </>
                ) : (
                  <>
                    <svg
                      className="copy-icon"
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                      <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                    </svg>
                    Copy
                  </>
                )}
              </button>
            </div>
            <div className="response-content">
              {field.responses[field.activeResponse || 0]
                .split("\n")
                .map((line, i) => (
                  <p key={i}>{line}</p>
                ))}
            </div>
          </div>
        )}
      </div>
    ))}
  </div>
</div>
      </div>

      {/* Floating action button for adding new fields */}
      <button onClick={addField} className="floating-add-button">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <line x1="12" y1="5" x2="12" y2="19"></line>
          <line x1="5" y1="12" x2="19" y2="12"></line>
        </svg>
      </button>
    </main>
  )
}

