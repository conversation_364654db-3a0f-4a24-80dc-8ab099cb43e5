{"name": "instagram-video-downloader", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 3010 --turbopack", "build": "next build", "build:locales": "python scripts/build_locales.py", "start": "next start -p 3010", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@tanstack/react-query": "^5.71.10", "@tanstack/react-query-devtools": "^5.71.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "js-cookie": "^3.0.5", "lucide-react": "^0.487.0", "next": "15.2.4", "next-intl": "^4.0.2", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "sonner": "^2.0.3", "tailwind-merge": "^3.1.0", "tw-animate-css": "^1.2.5", "user-agents": "^1.1.613", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@tanstack/eslint-plugin-query": "^5.71.5", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/user-agents": "^1.0.4", "eslint": "^9", "eslint-config-next": "15.2.4", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4", "typescript": "^5"}, "prettier": {"arrowParens": "always", "trailingComma": "es5", "tabWidth": 2, "semi": true, "singleQuote": false, "proseWrap": "always", "printWidth": 80, "plugins": ["prettier-plugin-tailwindcss"]}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}