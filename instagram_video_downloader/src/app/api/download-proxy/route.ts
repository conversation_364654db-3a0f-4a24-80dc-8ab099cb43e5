// app/api/download-proxy/route.ts
import { NextRequest, NextResponse } from "next/server";
import { writeFile } from "fs/promises";
import { join } from "path";
import * as fs from 'fs';

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "GET,OPTIONS,POST",
  "Access-Control-Allow-Headers": "Content-Type",
};

export async function OPTIONS() {
  return new NextResponse(null, { status: 204, headers: corsHeaders });
}

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const fileUrl = searchParams.get("url");
  const filename = searchParams.get("filename") || "gram-grabberz-video.mp4";

  if (!fileUrl) {
    return NextResponse.json(
      { error: "missingUrl", message: "url is required" },
      { status: 400, headers: corsHeaders }
    );
  }

  try {
    if (!fileUrl.startsWith("https://")) {
      return NextResponse.json(
        { error: "Invalid URL format" },
        { status: 400, headers: corsHeaders }
      );
    }

    // Fetch the video from the external URL
    const videoResponse = await fetch(fileUrl);

    if (!videoResponse.ok) {
      throw new Error(`Failed to fetch video: ${videoResponse.statusText}`);
    }

    // Read the video data as a buffer
    const arrayBuffer = await videoResponse.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Save to /tmp directory which is mounted as a volume in Docker
    const tempPath = join('/tmp', filename);
    await writeFile(tempPath, buffer);
    
    console.log(`File saved to: ${tempPath}`);
    
    // Check if file exists after writing
    try {
      const stats = await fs.promises.stat(tempPath);
      console.log(`File size: ${stats.size} bytes`);
    } catch (err) {
      console.error(`Error checking file: ${err}`);
    }

    // Return only the path to the saved file
    return NextResponse.json(
      { path: tempPath },
      { status: 200, headers: corsHeaders }
    );
  } catch (error: any) {
    console.error("Download proxy error:", error);
    return NextResponse.json(
      { error: "serverError", message: error.message },
      { status: 500, headers: corsHeaders }
    );
  }
}
