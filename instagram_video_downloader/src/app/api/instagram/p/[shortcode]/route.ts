import { NextRequest, NextResponse } from "next/server";
import { IG_GraphQLResponseDto } from "@/features/api/_dto/instagram";
import { getInstagramPostGraphQL } from "./utils";

interface RouteContext {
  params: Promise<{
    shortcode: string;
  }>;
}

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "GET,OPTIONS,POST",
  "Access-Control-Allow-Headers": "Content-Type",
};

export async function OPTIONS() {
  return new NextResponse(null, { status: 204, headers: corsHeaders });
}

export async function GET(_: NextRequest, context: RouteContext) {
  const { shortcode } = await context.params;

  if (!shortcode) {
    return NextResponse.json(
      { error: "noShortcode", message: "shortcode is required" },
      { status: 400, headers: corsHeaders }
    );
  }

  try {
    const response = await getInstagramPostGraphQL({
      shortcode,
    });

    const status = response.status;

    if (status === 200) {
      const { data } = (await response.json()) as IG_GraphQLResponseDto;
      if (!data.xdt_shortcode_media) {
        return NextResponse.json(
          { error: "notFound", message: "post not found" },
          { status: 404, headers: corsHeaders }
        );
      }

      if (!data.xdt_shortcode_media.is_video) {
          return NextResponse.json(
            {
              isPhoto: true,
              data,
              message: "post is a photo, not a video",
            },
            { status: 200, headers: corsHeaders }
          );
}

      return NextResponse.json({ data }, { status: 200, headers: corsHeaders });
    }

    if (status === 404) {
      return NextResponse.json(
        { error: "notFound", message: "post not found" },
        { status: 404, headers: corsHeaders }
      );
    }

    if (status === 429 || status === 401) {
      // Implement exponential backoff retry
      const retryCount = 3;
      for (let i = 0; i < retryCount; i++) {
        // Wait with exponential backoff (1s, 2s, 4s)
        await new Promise(r => setTimeout(r, 1000 * Math.pow(2, i)));
        
        const retryResponse = await getInstagramPostGraphQL({ shortcode });
        if (retryResponse.status === 200) {
          const { data } = (await retryResponse.json()) as IG_GraphQLResponseDto;
          return NextResponse.json({ data }, { status: 200, headers: corsHeaders });
        }
      }
      
      return NextResponse.json(
        {
          error: "tooManyRequests",
          message: "too many requests, try again later",
        },
        { status: 429, headers: corsHeaders }
      );
    }

    throw new Error("Failed to fetch post data");
  } catch (error: any) {
    console.error(error);
    return NextResponse.json(
      { error: "serverError", message: error.message },
      { status: 500, headers: corsHeaders }
    );
  }
}
