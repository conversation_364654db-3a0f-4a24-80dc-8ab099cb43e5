# node_backend/Dockerfile

# Use a specific LTS version for stability
FROM node:18-alpine

WORKDIR /usr/src/app

# Copy dependency definition files first for better caching
COPY package*.json ./

# Install dependencies (including devDependencies for build)
RUN npm install
RUN npm install user-agents@1.1.566 --force

# Copy the rest of the application code
COPY . .

# Build steps (run as root for permissions)
RUN npm run build:locales || true
RUN npm run build

# Add non-root user and change ownership
RUN addgroup -S appgroup && adduser -S appuser -G appgroup && \
    chown -R appuser:appgroup /usr/src/app

USER appuser


EXPOSE 3010

CMD [ "npm", "run", "start" ]
