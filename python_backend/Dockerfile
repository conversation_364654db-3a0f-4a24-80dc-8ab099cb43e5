# python_backend/Dockerfile

# Use a specific slim Python version
FROM python:3.12

ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1
ENV DEBIAN_FRONTEND=noninteractive

WORKDIR /app

# Install system dependencies (keep only essentials if possible)
# Combine RUN commands to reduce layers
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    # For OpenCV (if still needed after checking imports)
    # libgl1-mesa-glx libglib2.0-0 \ 
    # For Rettiwt (or other libs potentially needing compilation)
    python3-dev gcc libc-dev \ 
    # For Chrome/Chromedriver (Consider if absolutely necessary, maybe use selenium/standalone-chrome image?)
    wget unzip fonts-liberation libasound2 libgbm1 libnss3 libx11-xcb1 libxcomposite1 libxrandr2 libxdamage1 libxi6 libxtst6 xdg-utils \
    && rm -rf /var/lib/apt/lists/*


# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
RUN apt-get -y update
RUN apt-get -y upgrade
RUN apt-get install -y ffmpeg

# Copy application code
COPY . .

# Ensure downloaded media directory exists and has correct permissions
RUN mkdir -p /app/tweet_media

RUN apt-get install libatk1.0-0 libatk-bridge2.0-0 libcups2 libatspi2.0-0  -y
RUN pip install playwright && playwright install

# Expose the port FastAPI runs on
EXPOSE 8000

# Command to run the FastAPI server using Uvicorn
# Assumes your FastAPI app instance is named 'app' in 'main.py'. Adjust if needed.
# Specify the entrypoint command to run the FastAPI application
ENTRYPOINT python3 main.py

