# zero_code/main.py
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from .routers import generate_response
import os


class Nyx:
    def __init__(self):
        self.app = FastAPI()
        self.setup_routers()
        self.setup_cors()

    def setup_routers(self):
        self.app.include_router(generate_response.router)

    def setup_cors(self):
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )


def create_app():
    nyx = Nyx()
    return nyx.app
