from fastapi import APIRouter, Body
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import List
import os
import openai
import cv2
import base64
from typing import Any
import re
import requests

# Directory to save media
download_dir = "tweet_media"
os.makedirs(download_dir, exist_ok=True)

OPENAI_API_KEY = "********************************************************"  # Replace with env var ideally
bearer_token = "AAAAAAAAAAAAAAAAAAAAAIvGrQEAAAAAjPcTuuFSWhK%2Fvjd8B5L%2BdYv9WkE%3DyabUj9jDk3HE9tS5bbbwsreuH66TpyGKBqP71XQFlO7r8NdDAK"  # Replace with env var ideally

router = APIRouter()

# Initialize OpenAI client (handle potential missing key)
client = None
if OPENAI_API_KEY:
    try:
        client = openai.OpenAI(api_key=OPENAI_API_KEY)
        print("OpenAI client initialized.")
    except Exception as e:
        print(f"Warning: Failed to initialize OpenAI client - {e}")
else:
    print("Warning: OPENAI_API_KEY not found. OpenAI features will be disabled.")

# Define common media extensions (lowercase)
VIDEO_EXTENSIONS = {".mp4", ".mov", ".avi", ".mkv", ".wmv", ".flv", ".webm"}
IMAGE_EXTENSIONS = {".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".webp"}


messages = [
    {
        "role": "system",
        "content": """
                You are Nikki — a fictional, fabulous, and fearless social media influencer created by NYX Pro Makeup US. You live on TikTok, Instagram, and X, where you chat with your glam fam like they’re your BFFs.

Nikki is:

    Fun, casual, and expressive

    Obsessed with all things beauty and self-expression

    Supportive, hype-girly, sometimes dramatic (in the best way)

    Always matching the vibe of the user (silly, sarcastic, sweet, etc.)

    \n\n
    ```
Your voice is:

    **Full of personality and humor**

    **Written in lowercase (unless it’s for DRAMA)**

    **Emoji-forward, but never spammy — use emojis to vibe-match or enhance**

    **Often short, punchy, or a bit chaotic in the best way**
```
    \n\n

    ```
Your replies are:

    **Super short — think 1 line max, sometimes just a few words**

    ** NEVER say you “can’t wait to try it” — you already have**

    **Casual lowercase, unless you're being dramatic ON PURPOSE**

    **Full of attitude, love, or LOLs (depending on the vibe)**

    **Emoji-forward — but always vibey, never spammy**

    **Never corporate, never helpful — you're here to hype, react, flirt, or sass**

    **You match the user’s tone. If they’re silly, you’re sillier. If they’re dramatic, you’re worse**

```
    \n\n
You can use (but aren't limited to) these emojis: ⭐✨🩷💅💖💞💕👯‍♀️💃🤩😍💄😭🌟

Your job is to reply to user comments, captions, posts, or DMs like a real influencer bestie would. You don’t give customer service — you give ENERGY.
```
 **DO NOT sound like a chatbot. Be chaotic if needed. **
 **DO NOT explain or clarify. Just feel. **
 **DO NOT use emojis without a reason. ** 
Now, respond to each new input as Nikki — adapt to the user's tone and media, always staying true to your personality.
""",
    },
]


class TweetDetailsPayload(BaseModel):
    tweetData: Any


class TikTokPayload(BaseModel):
    video_url: Any


class InstagramPayload(BaseModel):
    path: str


class InstagramPayloadPhoto(BaseModel):
    photo_data: Any


class AnalysisResponse(BaseModel):
    response: List[str]


def remove_links(text):
    return re.sub(r"https?://\S+", "", text).strip()


@router.post("/generate_response")
async def generate_response(payload: TweetDetailsPayload):
    tweet = payload.tweetData
    clean_text = remove_links(tweet["fullText"])
    if not tweet["id"]:
        print("Error: Missing 'tweet' (ID) in request body.")
        return JSONResponse(
            status_code=400, content={"message": "Missing 'tweet' (ID) in request body"}
        )

    # Initialize variables for categorization
    video_files = []
    image_files = []
    other_files = []  # Optional: to catch unexpected file types

    try:
        tweet_messages = messages.copy()
        tweet_messages.append({"role": "user", "content": clean_text})
        if tweet.get("media", None):
            for file_path in tweet["media"]:
                try:
                    _, extension = os.path.splitext(file_path["url"])
                    extension_lower = extension.lower()
                    print(extension_lower)
                    print(file_path["url"])

                    if extension_lower in VIDEO_EXTENSIONS:
                        video_files.append(file_path["url"])
                    elif extension_lower in IMAGE_EXTENSIONS:
                        image_files.append(file_path["url"])
                    else:
                        if extension:
                            other_files.append(file_path["url"])
                        print(f"Note: Uncategorized media type: {file_path}")

                except Exception as e:
                    print(
                        f"Warning: Could not process media file path '{file_path}': {e}"
                    )

        print(f"Categorized Videos: {video_files}")
        print(f"Categorized Images: {image_files}")
        if other_files:
            print(f"Other Media Types: {other_files}")

        for media in video_files:
            response = requests.get(media)
            video = cv2.VideoCapture(response.content)
            base64Frames = []
            while video.isOpened():
                success, frame = video.read()
                if not success:
                    break
                _, buffer = cv2.imencode(".jpg", frame)
                base64Frames.append(base64.b64encode(buffer).decode("utf-8"))
            video.release()
            tweet_messages.append(
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image_url",
                            "image_url": {"url": f"data:image/jpeg;base64,{x}"},
                        }
                        for x in base64Frames[::100]
                    ],
                }
            )
        for media in image_files:
            response = requests.get(media)
            base64_image = base64.b64encode(response.content).decode("utf-8")
            tweet_messages.append(
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}"
                            },
                        },
                    ],
                }
            )

        response_completion = client.chat.completions.create(
            model="ft:gpt-4o-2024-08-06:projectniki:leilani:ANCI7OdH",
            messages=tweet_messages,
            max_tokens=128,
            n=3,
        )
        print("response_completion done")
        analysis_results_list = []
        if response_completion.choices:
            for choice in response_completion.choices:
                if choice.message and choice.message.content:
                    analysis_results_list.append(choice.message.content.strip())
                else:
                    analysis_results_list.append(
                        "Error: Received an empty response choice."
                    )
        else:
            # Handle cases where no choices are returned
            analysis_results_list = ["Error: No response choices received from OpenAI."]

        response_content_model = AnalysisResponse(response=analysis_results_list)
        return JSONResponse(content=response_content_model.model_dump())

    except Exception as e:
        # Catch errors during fetch_tweet_media or subsequent processing
        print(f"Error processing tweet {tweet["id"]}: {e}")
        # Log the full traceback ideally
        return JSONResponse(
            status_code=500, content={"message": f"Error processing tweet: {e}"}
        )


ms_token = "q-pIj4EPOaT6YpldZfkVuIvAFccGunstB_Zootp2LVlwPdWf2lOFNhBcrwxQH9CaiNwfcsMuE8a0oS5P5QVLReVLaBnwTkalMVKkC5FSsugXydjYmp00HKC7ER9elg=="
from TikTokApi import TikTokApi
import requests
import tempfile, os, base64, requests, cv2


@router.post("/generate_tiktok_response")
async def generate_response_tiktok(video_url: TikTokPayload):
    api = TikTokApi()
    await api.create_sessions(
        ms_tokens=[ms_token],
        num_sessions=1,
        sleep_after=3,
        browser=os.getenv("TIKTOK_BROWSER", "chromium"),
    )
    video_info = await api.video(
        url=video_url.video_url,
    ).info()
    i, session = api._get_session()
    downloadAddr = video_info["video"]["downloadAddr"]
    tiktok_messages = messages.copy()
    tiktok_messages.append({"role": "user", "content": video_info["desc"]})
    cookies = await api.get_session_cookies(session)
    h = session.headers
    h["range"] = "bytes=0-"
    h["accept-encoding"] = "identity;q=1, *;q=0"
    h["referer"] = "https://www.tiktok.com/"
    resp = requests.get(downloadAddr, headers=h, cookies=cookies)
    with tempfile.NamedTemporaryFile(delete=False, suffix=".mp4") as tmpf:
        tmpf.write(resp.content)
        tmp_path = tmpf.name
    video = cv2.VideoCapture(tmp_path)
    base64Frames = []
    while video.isOpened():
        success, frame = video.read()
        if not success:
            break
        _, buffer = cv2.imencode(".jpg", frame)
        base64Frames.append(base64.b64encode(buffer).decode("utf-8"))
        video.release()
        tiktok_messages.append(
            {
                "role": "user",
                "content": [
                    {
                        "type": "image_url",
                        "image_url": {"url": f"data:image/jpeg;base64,{x}"},
                    }
                    for x in base64Frames[::100]
                ],
            }
        )
    response_completion = client.chat.completions.create(
        model="ft:gpt-4o-2024-08-06:projectniki:leilani:ANCI7OdH",
        messages=tiktok_messages,
        max_tokens=128,
        n=3,
    )
    analysis_results_list = []
    if response_completion.choices:
        for choice in response_completion.choices:
            if choice.message and choice.message.content:
                analysis_results_list.append(choice.message.content.strip())
            else:
                analysis_results_list.append(
                    "Error: Received an empty response choice."
                )
    else:
        # Handle cases where no choices are returned
        analysis_results_list = ["Error: No response choices received from OpenAI."]

    response_content_model = AnalysisResponse(response=analysis_results_list)
    return JSONResponse(content=response_content_model.model_dump())


@router.post("/generate_instagram_response_video")
def generate_response_instagram(payload: InstagramPayload):
    path = payload.path
    print(f"Received path: {path}")

    # Debug: List files in /tmp directory
    print("Files in /tmp directory:")
    try:
        for file in os.listdir("/tmp"):
            file_path = os.path.join("/tmp", file)
            file_size = (
                os.path.getsize(file_path) if os.path.isfile(file_path) else "directory"
            )
            print(f"  - {file} ({file_size})")
    except Exception as e:
        print(f"Error listing /tmp: {e}")

    if not os.path.exists(path):
        # Try to handle Docker volume path mapping
        print(f"Path not found: {path}")

        # Try alternative paths
        alt_paths = [path, path.replace("/tmp/", "/app/tmp/"), f"/app{path}"]

        for alt_path in alt_paths:
            print(f"Trying alternative path: {alt_path}")
            if os.path.exists(alt_path):
                print(f"Found file at: {alt_path}")
                path = alt_path
                break
        else:
            return JSONResponse(
                status_code=400,
                content={
                    "message": f"File not found at path: {path}. Make sure volumes are properly mounted between services."
                },
            )
    video = cv2.VideoCapture(path)
    base64Frames = []
    while video.isOpened():
        success, frame = video.read()
        if not success:
            break
        _, buffer = cv2.imencode(".jpg", frame)
        base64Frames.append(base64.b64encode(buffer).decode("utf-8"))
        video.release()
        instagram_messages = messages.copy()
        instagram_messages.append(
            {
                "role": "user",
                "content": [
                    {
                        "type": "image_url",
                        "image_url": {"url": f"data:image/jpeg;base64,{x}"},
                    }
                    for x in base64Frames[::100]
                ],
            }
        )
    response_completion = client.chat.completions.create(
        model="ft:gpt-4o-2024-08-06:projectniki:leilani:ANCI7OdH",
        messages=instagram_messages,
        max_tokens=128,
        n=3,
    )
    analysis_results_list = []
    if response_completion.choices:
        for choice in response_completion.choices:
            if choice.message and choice.message.content:
                analysis_results_list.append(choice.message.content.strip())
            else:
                analysis_results_list.append(
                    "Error: Received an empty response choice."
                )
    else:
        # Handle cases where no choices are returned
        analysis_results_list = ["Error: No response choices received from OpenAI."]

    response_content_model = AnalysisResponse(response=analysis_results_list)
    return JSONResponse(content=response_content_model.model_dump())


@router.post("/generate_instagram_response_photo")
def generate_response_instagram(photo_data: InstagramPayloadPhoto):
    data = photo_data.photo_data["data"]["xdt_shortcode_media"]
    post_text = data["edge_media_to_caption"]["edges"][0]["node"]["text"]
    instagram_messages = messages.copy()
    instagram_messages.append({"role": "user", "content": post_text})
    image_url = data["display_url"]
    response = requests.get(image_url)
    base64_image = base64.b64encode(response.content).decode("utf-8")
    instagram_messages.append(
        {
            "role": "user",
            "content": [
                {
                    "type": "image_url",
                    "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"},
                },
            ],
        }
    )

    response_completion = client.chat.completions.create(
        model="ft:gpt-4o-2024-08-06:projectniki:leilani:ANCI7OdH",
        messages=instagram_messages,
        max_tokens=128,
        n=3,
    )
    analysis_results_list = []
    if response_completion.choices:
        for choice in response_completion.choices:
            if choice.message and choice.message.content:
                analysis_results_list.append(choice.message.content.strip())
            else:
                analysis_results_list.append(
                    "Error: Received an empty response choice."
                )
    response_content_model = AnalysisResponse(response=analysis_results_list)
    return JSONResponse(content=response_content_model.model_dump())
