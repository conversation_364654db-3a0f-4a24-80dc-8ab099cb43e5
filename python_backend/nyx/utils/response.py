import requests
import os
import tweepy
from typing import <PERSON><PERSON>, List


def fetch_tweet_media(
    tweet_id: str, bearer_token: str, download_dir: str = "tweet_media"
) -> <PERSON><PERSON>[str, List[str]]:
    """
    Fetches the text and media of a tweet and downloads the media.

    Parameters:
        tweet_id (str): The ID of the tweet.
        bearer_token (str): The Twitter API bearer token.
        download_dir (str): The directory to save the downloaded media.

    Returns:
        Tuple[str, List[str]]: The tweet text and a list of downloaded media file paths.
    """
    os.makedirs(download_dir, exist_ok=True)  # Create directory if it doesn't exist

    client = tweepy.Client(bearer_token=bearer_token)

    tweet = client.get_tweet(
        tweet_id,
        expansions=["attachments.media_keys"],
        media_fields=["url", "preview_image_url", "variants", "type"],
        tweet_fields=["text"],
    )

    tweet_text = tweet.data.get("text", "") if tweet.data else ""
    media_files = []

    if tweet.includes and "media" in tweet.includes:
        for media in tweet.includes["media"]:
            media_url = None
            file_extension = None

            if media.type == "photo":
                media_url = media.url
                if media_url:
                    file_extension = media_url.split(".")[-1].split("?")[0]
            elif media.type in ["video", "animated_gif"]:
                if media.variants:
                    valid_variants = [
                        v
                        for v in media.variants
                        if v.get("bit_rate") is not None
                        and "video" in v.get("content_type", "")
                    ]
                    if valid_variants:
                        best_variant = max(valid_variants, key=lambda v: v["bit_rate"])
                        media_url = best_variant["url"]
                        content_type = best_variant.get("content_type", "")
                        file_extension = "mp4" if "mp4" in content_type else "webm"
                if not media_url and media.preview_image_url:
                    media_url = media.preview_image_url
                    file_extension = media_url.split(".")[-1].split("?")[0]
            else:
                media_url = getattr(media, "url", None)
                if media_url:
                    file_extension = media_url.split(".")[-1].split("?")[0]

            if media_url and file_extension:
                try:
                    response = requests.get(media_url, stream=True)
                    response.raise_for_status()
                    filename = os.path.join(
                        download_dir, f"{tweet_id}_{media.media_key}.{file_extension}"
                    )
                    with open(filename, "wb") as f:
                        for chunk in response.iter_content(chunk_size=8192):
                            f.write(chunk)
                    media_files.append(filename)
                except requests.exceptions.RequestException as e:
                    print(f"Error downloading media: {e}")

    return tweet_text, media_files
