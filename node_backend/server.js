import express from 'express';
import { Rettiwt } from 'rettiwt-api';
import axios from 'axios'; // Or use node-fetch or built-in fetch
import dotenv from 'dotenv';
import cors from 'cors'; // Import cors


dotenv.config(); // Load environment variables from .env file

const app = express();
const port = process.env.PORT || 3001; // Use port from .env or default to 3001

// --- Environment Variable Check ---
const apiKey = process.env.RETTIWT_API_KEY;
const pythonBackendUrl = process.env.PYTHON_BACKEND_URL;

if (!apiKey) {
  console.error("FATAL ERROR: RETTIWT_API_KEY is not defined in .env file.");
  process.exit(1); // Stop the server if the key is missing
}
if (!pythonBackendUrl) {
    console.error("FATAL ERROR: PYTHON_BACKEND_URL is not defined in .env file.");
    process.exit(1); 
}

// --- Middleware ---
app.use(cors()); // Enable CORS for requests from your frontend (React app)
app.use(express.json()); // Middleware to parse JSON request bodies

// --- Rettiwt Instance ---
// Use the API Key from the environment variable
const rettiwt = new Rettiwt({ apiKey: apiKey });

// --- API Endpoint ---
app.post('/api/process-tweet', async (req, res) => {
  const { tweetId } = req.body;

  if (!tweetId) {
    return res.status(400).json({ message: "Missing 'tweetId' in request body" });
  }

  console.log(`Received request for tweet ID: ${tweetId}`);

  try {
    // 1. Fetch tweet details using rettiwt
    console.log(`Fetching details for tweet ${tweetId} from Twitter...`);
    const tweetDetails = await rettiwt.tweet.details(tweetId);
    console.log(`Successfully fetched details for tweet ${tweetId}.`);

    // 2. Forward tweet details to Python backend
    console.log(`Forwarding tweet details to Python backend at ${pythonBackendUrl}`);
    const pythonResponse = await axios.post(pythonBackendUrl, {
      // Send the details nested under a key Python expects (e.g., 'tweetData')
      tweetData: tweetDetails 
    });
    console.log(`Received response from Python backend.`);

    // 3. Relay Python's response back to the frontend
    // Assuming Python responds with JSON like { "response": [...] }
    res.status(pythonResponse.status).json(pythonResponse.data); 

  } catch (error) {
    console.error(`Error processing tweet ${tweetId}:`, error.message);

    // Handle errors from Rettiwt or the Python backend call
    if (axios.isAxiosError(error) && error.response) {
        // Error calling Python backend
        console.error('Error response from Python backend:', error.response.data);
        res.status(error.response.status || 500).json({ 
            message: "Error communicating with Python backend", 
            details: error.response.data 
        });
    } else if (error.constructor.name === 'RettiwtError') { 
        // Error from rettiwt-api
         res.status(500).json({ message: "Error fetching tweet details from Twitter", details: error.message });
    }
     else {
        // Generic server error
        res.status(500).json({ message: "An internal server error occurred in Node.js backend", details: error.message });
    }
  }
});

// --- Start Server ---
app.listen(port, () => {
  console.log(`Node.js backend listening on port ${port}`);
  console.log(`Forwarding requests to Python backend at ${pythonBackendUrl}`);
}); 