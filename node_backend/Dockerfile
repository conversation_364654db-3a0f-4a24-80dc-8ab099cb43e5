# node_backend/Dockerfile

# Use a specific LTS version for stability
FROM node:18-alpine

WORKDIR /usr/src/app

# Add non-root user
RUN addgroup -S appgroup && adduser -S appuser -G appgroup

# Copy dependency definition files
COPY package*.json ./

# Install production dependencies only
RUN npm ci --only=production

# Copy application code
COPY . .

# Change ownership to non-root user
RUN chown -R appuser:appgroup /usr/src/app

# Switch to non-root user
USER appuser

# Expose the port (Ensure it matches server.js and docker-compose)
EXPOSE 3001

# Command to run the server
CMD [ "node", "server.js" ]